package model

import (
	"fmt"
	"log/slog"
	"math/rand/v2"
	"strconv"
	"strings"
	"time"

	"git.7k7k.com/data/abAdmin/gopkg/condition"
	adminmodel "git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/model/logic"
	"git.7k7k.com/pkg/storage/codec"
	"github.com/spaolacci/murmur3"
	"github.com/spf13/cast"
)

const (
	DomainMod = 1000 // 域 mod 数
	BucketMod = 1000 // 桶 mod 数

	LayerDivTypeHash = 1
	ExpAlgoHash      = 1
)

type (
	ProjectID = int
	LayerID   = int
)

// 项目
// key = $project
type ProjectIdx struct {
	ID               int                             `json:"id,string"`              // ID
	Key              string                          `json:"key"`                    // 项目key。客户端使用的标识
	Layers           []int                           `json:"layers"`                 // 普通层IDs
	LayerExclusiveID int                             `json:"layer_exclusive_id"`     // 独占实验层ID
	GroupParamStyle  logic.GroupParamStyle           `json:"group_param_style"`      // 实验组参数样式
	ConflictRule     adminmodel.FeatureConflictRules `json:"conflict_rule,omitzero"` // 冲突列表
	Domains          []*Domain                       `json:"domains"`                // 域
}

func (m ProjectIdx) MetricKey() string { return fmt.Sprintf("%d-%s", m.ID, m.Key) }

func (m *ProjectIdx) PickDomain(uid string) *Domain {
	hash := murmur3.Sum64([]byte("pick_domain:" + uid))
	w := int(hash % uint64(DomainMod))
	for _, d := range m.Domains {
		if w < int(d.ModRight) {
			return d
		}
	}
	return nil
}

type Domain struct {
	LayerIDs []LayerID `json:"layer_ids"`
	ModRight int32     `json:"mod_right"` // mod 右区间，开区间。过程可能会超过65535,使用 int32
}

const (
	LayerLevelZero = int32(0)
	LayerLevelLow  = int32(adminmodel.LayerLevelLow)
	LayerLevelMid  = int32(adminmodel.LayerLevelMedium)
	LayerLevelHig  = int32(adminmodel.LayerLevelHigh)
)

// 实验层
// key = $layer
type LayerIdx struct {
	ID              int            `json:"id,string"`          // ID
	Name            string         `json:"name"`               // 名称
	Level           int32          `json:"level"`              // 层优先级
	Exclusive       bool           `json:"exclusive"`          // 是否独占层
	Prop            int16          `json:"prop"`               // 独占层流量比例
	UserWhiteList   map[string]int `json:"user_white_list"`    // 用户ID => 实验组ID
	DivType         int8           `json:"div_type"`           // 分流类型：1:比例 2:权重
	Mod             int32          `json:"mod"`                // 分流hash模数
	ModKey          []string       `json:"mod_key"`            // 分流hash入参：uid,did
	BucketStr       string         `json:"bucket_str"`         // 桶状态字符串，用来存到 Redis
	PrjID           int            `json:"prj_id,string"`      // 项目ID
	ExpIDs          []int          `json:"exp_ids"`            // 实验IDs
	GroupIDs        []int          `json:"group_ids"`          // 实验组IDs，仅包含运行中的组
	PendingGroupIDs []int          `json:"pending_group_ids"`  // 实验组IDs，仅包含待开始的组
	CachedAt        time.Time      `json:"cached_at,omitzero"` // 当前缓存的更新时间
	Seed            string         `json:"seed"`               // 随机种子
	LayerIdxStat

	// 本地结构
	Buckets []Bucket `json:"buckets"` // 桶
}

type LayerIdxStat struct {
	BucketUsed   int                 `json:"bucket_used"`    // 已使用桶数
	ExpGroupUsed map[int]map[int]int `json:"exp_group_used"` // 实验ID => 实验组ID => 已使用桶数
}

func (m *LayerIdx) GetBuckets() []Bucket {
	if m.Buckets == nil {
		_ = codec.Default.UnmarshalFromString(m.BucketStr, &m.Buckets)
	}
	return m.Buckets
}

func (m *LayerIdx) SetBuckets(buckets []Bucket) {
	m.Buckets = buckets

	var err error
	m.BucketStr, err = codec.Default.MarshalToString(buckets)
	if err != nil {
		slog.Error("marshal bucket", "error", err.Error())
	}

	// 统计
	m.BucketUsed = 0
	m.ExpGroupUsed = make(map[int]map[int]int)
	for _, b := range buckets {
		if !b.IsEmpty() {
			m.BucketUsed++
			if m.ExpGroupUsed[b.ExpId] == nil {
				m.ExpGroupUsed[b.ExpId] = make(map[int]int)
			}
			m.ExpGroupUsed[b.ExpId][b.GroupId]++
		}
	}
}

func (m *LayerIdx) DebugState() string {
	s := make([]byte, len(m.Buckets))
	for i, b := range m.Buckets {
		if b.IsEmpty() {
			s[i] = ' '
		} else {
			s[i] = '#'
		}
	}
	return string(s)
}

func (m *LayerIdx) SortBy() int {
	e := 1
	if m.Exclusive {
		e = 0
	}
	return e<<32 + m.ID
}

func (m *LayerIdx) GetDomainID() int {
	if m.Exclusive {
		return 0
	}

	return 1
}

func (m *LayerIdx) GetSeed() string {
	return m.Seed
}

func (m *LayerIdx) GenerateSeed() string {
	s := make([]string, 0, len(m.GroupIDs))
	for _, id := range m.GroupIDs {
		s = append(s, strconv.Itoa(id))
	}
	return strings.Join(s, ",")
}

const (
	BucketStateEmpty   = 0 // 从0开始，新建的model默认值也是0
	BucketStateBooking = 2
	BucketStateUsing   = 3
)

// Bucket
// 这里要大量序列化，所以字段名要尽量短
type Bucket struct {
	State   int8 `json:"-"` // 状态：0:空白 2:预订中 3:使用中
	GroupId int  `json:"-"` // 实验组ID
	ExpId   int  `json:"-"` // 实验ID
}

func (m Bucket) IsEmpty() bool {
	return m.State == BucketStateEmpty
}

func (m *Bucket) Clear() {
	m.State = BucketStateEmpty
	m.GroupId = 0
	m.ExpId = 0
}

func (b Bucket) MarshalJSON() ([]byte, error) {
	if b.IsEmpty() {
		return []byte("[]"), nil
	}
	return []byte(fmt.Sprintf(`[%d,%d,%d]`, b.ExpId, b.GroupId, b.State)), nil
}

func (b *Bucket) UnmarshalJSON(data []byte) error {
	// 处理空值情况
	if len(data) == 0 || string(data) == "[]" {
		b.Clear()
		return nil
	}

	// 检查基本格式 [x,x,x]
	if len(data) < 5 || data[0] != '[' || data[len(data)-1] != ']' {
		return fmt.Errorf("invalid bucket format: %s", string(data))
	}

	// 去掉首尾的 [ ]
	s := string(data[1 : len(data)-1])

	// 解析三个数字
	nums := make([]int, 3)
	idx := 0
	start := 0

	for i := 0; i <= len(s); i++ {
		if i == len(s) || s[i] == ',' {
			if idx >= 3 {
				return fmt.Errorf("too many values in bucket: %s", string(data))
			}

			// 解析数字
			if start == i {
				return fmt.Errorf("empty value in bucket: %s", string(data))
			}

			val := 0
			for j := start; j < i; j++ {
				if s[j] < '0' || s[j] > '9' {
					return fmt.Errorf("invalid number in bucket: %s", string(data))
				}
				val = val*10 + int(s[j]-'0')
			}
			nums[idx] = val
			idx++
			start = i + 1
		}
	}

	if idx != 3 {
		return fmt.Errorf("invalid number of values in bucket: %s", string(data))
	}

	b.ExpId = nums[0]
	b.GroupId = nums[1]
	b.State = int8(nums[2])
	return nil
}

// 实验状态
// key = $exp
type Exp struct {
	ID              int                 `json:"id,string"`              // ID
	Status          int8                `json:"status"`                 // 状态：1:调试 2:灰度 3:运行 4:冻结 5:停止
	Prop            int16               `json:"prop"`                   // 流量比例，权重
	Algo            int8                `json:"algo"`                   // 分流算法：1:hash 2:普通轮询 3:CSR
	CSR             []*CSRConfig        `json:"csr,omitzero"`           // csr配置
	ActiveUType     int8                `json:"active_type"`            // 允许用户类型：1:新用户 2:老用户 3:所有用户
	Strategy        adminmodel.Strategy `json:"strategy,omitzero"`      // 策略
	RunningGroupIds []int               `json:"running_group_ids"`      // 运行中的实验组IDs
	AllGroupIds     []int               `json:"all_group_ids"`          // 所有实验组IDs，包含冻结
	PrjID           int                 `json:"prj_id,string"`          // 项目ID
	LayerID         int                 `json:"layer_id,string"`        // 实验层ID
	UserDuration    time.Duration       `json:"user_duration"`          // 用户停留时长，多长时间后自动自动退组
	StartTime       *time.Time          `json:"start_time,omitzero"`    // 开始时间
	RateLimit       *RateLimit          `json:"rate_limit_v2,omitzero"` // 限流
}

type RateLimit struct {
	Duration time.Duration `json:"duration"` // 限流时长
	Limit    int32         `json:"limit"`    // 限流次数
}

func (m Exp) IsPending() bool {
	return m.Status == int8(adminmodel.ExpStatusPending)
}

func (m Exp) IsGray() bool {
	return m.Status == int8(adminmodel.ExpStatusGrayRelease) || m.Status == int8(adminmodel.ExpStatusGrayReleaseNoNewFlow)
}

func (m Exp) IsClosed() bool {
	return adminmodel.IsClosed(int32(m.Status))
}

func (m Exp) IsFreezed() bool {
	return adminmodel.IsFreezed(int32(m.Status))
}

// 计算当前时间下需要分配的实验组
func (m *Exp) IntimeGrayRate(now time.Time) int {
	if m.StartTime == nil {
		return 100 // 全量
	}

	const GrayTimeSec = 1800 // 30min
	d := int(now.Sub(*m.StartTime).Seconds())
	r := d * 100 / GrayTimeSec
	if r > 100 {
		r = 100
	}

	return r
}

// CanEnter 是否能进组
func (exp *Exp) CanEnter(userLabels condition.Attr, hitCache bool) bool {
	// 实验状态
	if exp.IsClosed() {
		return false
	}

	// 用户类型：新增or活跃
	if exp.ActiveUType == adminmodel.ExpTypeNew || exp.ActiveUType == adminmodel.ExpTypeActive {
		v, _ := userLabels("active_type")
		if exp.ActiveUType != cast.ToInt8(v) {
			return false
		}
	}

	// 开启缓存持久化，且命中缓存，则直接返回true
	if hitCache && exp.Strategy.TriggerOnce {
		return true
	}

	// 准入条件
	return exp.Strategy.TriggerUserKey.Evaluate(userLabels)
}

func (m *Exp) StartAt() int64 {
	if m.StartTime == nil {
		return 0
	}

	return m.StartTime.Unix()
}

type (
	CSRConfig  = adminmodel.CSRData
	SplitGroup = adminmodel.SplitGroup
)

// 实验组状态。方案
type Group struct {
	ID              int                   `json:"id,string"`         // ID
	Key             string                `json:"key"`               // 实验组key：140-31 (Schema)
	Status          int8                  `json:"status"`            // 状态：1:空白 2:冻结 3:使用中
	GroupParamStyle logic.GroupParamStyle `json:"group_param_style"` // 实验组参数样式
	ParamJSON       string                `json:"param_json"`        // 参数
	ExpID           int                   `json:"exp_id,string"`     // 实验ID
	PrjID           int                   `json:"prj_id,string"`     // 项目ID
	LayerID         int                   `json:"layer_id,string"`   // 实验层ID
	GroupName       string                `json:"group_name"`        // 实验组名称
	RedirectTo      []*SplitGroup         `json:"redirect_to"`       // 重定向到的实验组
	FeatureKeys     []string              `json:"feature_keys"`      // 特征key
	FeatureIDs      []string              `json:"feature_ids"`       // 特征ID
}

func (m ProjectIdx) GetID() int { return m.ID }
func (m LayerIdx) GetID() int   { return m.ID }
func (m Exp) GetID() int        { return m.ID }
func (m Group) GetID() int      { return m.ID }

// NeedBucket 需要分配桶
func (m Group) NeedBucket() bool {
	return m.IsRunning() // 只有运行中要分新桶
}

func (m Group) IsRunning() bool {
	return m.Status == adminmodel.GroupStateRunning
}

func (m Group) IsFrozen() bool {
	return m.Status == adminmodel.GroupStateRunningNotProgress
}

// CalcRedirectForUser 给 uid 分配一个重定向Rule
// TODO 根据 uid hash 或 rand 来分
func (g *Group) CalcRedirectForUser(uid string) *SplitGroup {
	if len(g.RedirectTo) < 1 {
		return nil
	}

	if len(g.RedirectTo) == 1 {
		return g.RedirectTo[0]
	}

	// 按权重分配
	sum := 0
	for _, sg := range g.RedirectTo {
		sum += int(sg.Rate)
	}

	r := rand.IntN(10000)
	if r > sum { // 一定不命中
		return nil
	}
	for _, sg := range g.RedirectTo {
		if r < int(sg.Rate) {
			return sg
		}
		r -= int(sg.Rate)
	}

	return g.RedirectTo[r]
}
