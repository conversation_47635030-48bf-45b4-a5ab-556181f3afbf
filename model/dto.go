package model

import (
	"strconv"

	"github.com/spf13/cast"
)

// ABLog 只记录分流成功的
type ABLog struct {
	ProjectKey string         `json:"project_key"`
	BundleID   string         `json:"bundle_id,omitzero"`
	LayerId    int            `json:"layer_id"`
	ExpID      int            `json:"exp_id,omitzero"`
	GroupID    int            `json:"group_id,omitzero"`
	GroupName  string         `json:"group_name,omitzero"`
	DistinctID string         `json:"distinct_id"`
	TraceID    string         `json:"trace_id"`
	LDebug     *LDebug        `json:"debug,omitzero"`
	Attr       map[string]any `json:"attr,omitzero"`
	Ts         int64          `json:"ts"`
	Hit        bool           `json:"hit"` // true: hit, false: miss
}

type Debug struct {
	DomainID int             `json:"domain_id,omitzero"`
	Layers   map[int]*LDebug `json:"layers,omitempty"`
}

type LDebug struct {
	HitWhite      bool `json:"hit_white,omitempty"`      // 命中白名单
	HitCache      bool `json:"hit_cache,omitempty"`      // 命中缓存
	LeaveCache    int  `json:"leave_cache,omitempty"`    // 退出缓存，值为实验组ID
	MissBucket    bool `json:"miss_bucket,omitempty"`    // 命中的桶没有分配实验
	TriggerFailed bool `json:"trigger_failed,omitempty"` // 触发条件未通过
	Redirect      bool `json:"redirect,omitempty"`       // 通过退组重定向进组
	ParamInvalid  bool `json:"param_invalid,omitempty"`  // 实验参数无法合并，不下发方案和埋点
	Conflict      bool `json:"conflict,omitempty"`       // 跟更高优先级的方案冲突，特性有裁剪，但依然会下发方案和埋点
	RateLimit     bool `json:"rate_limit,omitempty"`     // 限流
}

func (l *LDebug) HitCache01() string {
	return strconv.Itoa(cast.ToInt(l.HitCache))
}

func (ablog ABLog) PartitionKey() []byte {
	return []byte(ablog.TraceID)
}
