image: golang:1.24.4

variables:
  REPO_NAME: git.7k7k.com/data/abScheduler
  GOPATH: $CI_PROJECT_DIR/.go

cache:
  paths:
    - $GOPATH/pkg

before_script:
  - mkdir -p $GOPATH/src/$(dirname $REPO_NAME)
  - ln -svf $CI_PROJECT_DIR $GOPATH/src/$REPO_NAME
  - cd $GOPATH/src/$REPO_NAME
  - git config url."****************:".insteadOf "https://git.7k7k.com/"
  - git config --global url."****************:".insteadOf "https://git.7k7k.com/"
  - go version
  - go env -w GOPROXY=https://goproxy.cn,direct
  - go env -w GOPRIVATE=git.7k7k.com
  - mkdir -p ~/.ssh
  - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
  - chmod 600 ~/.ssh/id_rsa
  - ssh-keyscan git.7k7k.com >> ~/.ssh/known_hosts
  - go mod tidy
  - export LOG_DIR=/var/log

stages:
  - test
  - build
  - deploy


testing:
  stage: test
  coverage: '/total:\s+\(statements\)\s+(\d+.\d+\%)/' # 从 stdout 抽取覆盖率
  script:
    - go fmt $(go list ./... | grep -v /vendor/)
    - go vet $(go list ./... | grep -v /vendor/)
    - go test -gcflags="all=-N -l" -race -coverprofile=coverage.out.tmp $(go list ./... | grep -v /vendor/)
    - cat coverage.out.tmp | grep -v "_gen.go" | grep -v ".gen.go" > coverage.out
    - go tool cover -func=coverage.out  # 输出覆盖率到stdout
    - go tool cover -html=coverage.out -o coverage.html
  artifacts:
    paths:
      - coverage.out
      - coverage.html
    reports:
      codequality: coverage.html

# compile:
#   stage: build
#   script:
#     - go build -race -ldflags "-extldflags '-static'" -o $CI_PROJECT_DIR/mybinary
#   artifacts:
#     paths:
#       - mybinary
