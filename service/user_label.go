package service

import (
	"context"
	"log/slog"
	"maps"
	"strings"
	"time"

	"git.7k7k.com/data/abScheduler/gopkg/csr"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/pkg/common/gosentry"
	"github.com/getsentry/sentry-go"
)

// UserLabelService 用户标签服务
// @autowire(set=service)
type UserLabelService struct {
	RedisClts          *redises.ClientMgr
	UserStateService   *UserStateService
	UserHistoryService *UserHistoryService
}

type UserLabels struct {
	UserLabels  map[string]any
	StateLabels map[string]any
}

func (l *UserLabels) Get(key string) (any, bool) {
	v, ok := l.UserLabels[key]
	if !ok {
		v, ok = l.StateLabels[key]
	}
	return v, ok
}

func (s *UserLabelService) AppendStateLabel(labels map[string]any, state *HitState) *UserLabels {
	stateLabels := map[string]any{
		"exp_days": -1, // 进入实验天数
	}
	if state != nil && state.GroupID > 0 {
		stateLabels["exp_days"] = int(time.Since(time.Unix(int64(state.EnterAt), 0)).Hours() / 24)
	}

	return &UserLabels{
		UserLabels:  labels,
		StateLabels: stateLabels,
	}
}

// BuildUserLabels 获取用户标签
func (s *UserLabelService) BuildUserLabels(ctx context.Context, uid string, ctxAttr map[string]any, originLayersState LayersHitState) (labels map[string]any, err error) {
	span, finish := gosentry.StartSpan(ctx, "ab.UserLabels", "", "")
	defer finish()
	ctx = gosentry.WithTailSpan(ctx, span)

	labels = make(map[string]any, 16)

	// 离线特征：DMP、数仓
	// labelsDB, err := s.loadFromDB(ctx, uid)
	// if err != nil {
	// 	return
	// }
	// maps.Copy(labels, labelsDB)

	// 在线特征：请求上下文
	labelsCtx := ctxAttr
	maps.Copy(labels, labelsCtx)
	s.formatToLower(labels, "country")

	// 复合特征
	labels["running_exp_ids"] = []int{} // 正在进行中的实验
	labels["running_grp_ids"] = []int{} // 正在进行中的方案
	for _, state := range originLayersState {
		if state.GroupID > 0 {
			labels["running_exp_ids"] = append(labels["running_exp_ids"].([]int), state.ExpID)
			labels["running_grp_ids"] = append(labels["running_grp_ids"].([]int), state.GroupID)
		}
	}

	return
}

func (s *UserLabelService) loadFromDB(ctx context.Context, uid string) (labels map[string]any, err error) {
	span0 := gosentry.TailSpan(ctx)

	labels = make(map[string]any, 8)

	// CSR
	_, finish1 := gosentry.StartChild(span0, "db.redis", sentry.WithOpName("RedisCSR"))
	kv, _, err := csr.GetUserLabelFromRedisForCSR(ctx, s.RedisClts.UserLabelRedis, "prefixKey", uid)
	finish1()
	if len(kv) > 0 {
		maps.Copy(labels, kv)
	}

	// UserHistory 历史记录
	if false {
		labels["exp_joined"] = []int{}    // 参与过的实验
		labels["groups_joined"] = []int{} // 参与过的方案
		labels["history_exp_ids"] = []int{}
		labels["history_grp_ids"] = []int{}

		_, finish2 := gosentry.StartChild(span0, "db.query", sentry.WithOpName("GetHistory"))
		history, err := s.UserHistoryService.GetHistory(ctx, uid)
		finish2()
		if err != nil {
			slog.WarnContext(ctx, "GetHistory", "uid", uid, "err", err.Error())
		}
		if history != nil {
			labels["exp_joined"] = history.ExpIds
			labels["groups_joined"] = history.GrpIds
		}
		labels["history_exp_ids"] = labels["exp_joined"]
		labels["history_grp_ids"] = labels["groups_joined"]
	}

	return labels, nil
}

func (s *UserLabelService) formatToLower(labels map[string]any, key string) {
	if v, ok := labels[key]; ok {
		if vStr, ok := v.(string); ok {
			labels[key] = strings.ToLower(vStr)
		}
	}
}
