package abtest

import (
	"testing"
)

func TestRequest_ValidRequest_UIDValidation(t *testing.T) {
	tests := []struct {
		name    string
		uid     string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid uid with letters and numbers",
			uid:     "user123456789",
			wantErr: false,
		},
		{
			name:    "valid uid with underscores",
			uid:     "user_123_456",
			wantErr: false,
		},
		{
			name:    "valid uid with hyphens",
			uid:     "user-123-456",
			wantErr: false,
		},
		{
			name:    "valid uid mixed characters",
			uid:     "user_123-abc_XYZ",
			wantErr: false,
		},
		{
			name:    "valid uid with dots",
			uid:     "user.123.456",
			wantErr: false,
		},
		{
			name:    "invalid uid with spaces",
			uid:     "user 123456789",
			wantErr: true,
			errMsg:  "uid cannot contain spaces",
		},
		{
			name:    "invalid uid with special characters",
			uid:     "user@123456789",
			wantErr: true,
			errMsg:  "uid can only contain letters, numbers, underscores, hyphens, and dots",
		},
		{
			name:    "invalid uid with Chinese characters",
			uid:     "用户123456789",
			wantErr: true,
			errMsg:  "uid can only contain letters, numbers, underscores, hyphens, and dots",
		},
		{
			name:    "invalid uid too short",
			uid:     "user123",
			wantErr: true,
			errMsg:  "uid is too short",
		},
		{
			name:    "invalid empty uid",
			uid:     "",
			wantErr: true,
			errMsg:  "uid is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &Request{
				ProjectKey: "test_project",
				Uid:        tt.uid,
			}

			err := req.ValidRequest()

			if tt.wantErr {
				if err == nil {
					t.Errorf("ValidRequest() expected error but got none")
					return
				}
				if err.Error() != tt.errMsg {
					t.Errorf("ValidRequest() error = %v, want %v", err.Error(), tt.errMsg)
				}
			} else {
				if err != nil {
					t.Errorf("ValidRequest() unexpected error = %v", err)
				}
			}
		})
	}
}

func TestIsValidUID(t *testing.T) {
	tests := []struct {
		name string
		uid  string
		want bool
	}{
		{"valid letters only", "abcdefghijklmnopqrstuvwxyz", true},
		{"valid numbers only", "1234567890", true},
		{"valid mixed", "abc123XYZ", true},
		{"valid with underscores", "user_123_test", true},
		{"valid with hyphens", "user-123-test", true},
		{"valid with dots", "user.123.test", true},
		{"valid mixed all", "user_123-test.ABC", true},
		{"invalid with space", "user 123", false},
		{"invalid with at", "user@123", false},
		{"invalid with hash", "user#123", false},
		{"invalid with Chinese", "用户123", false},
		{"invalid with emoji", "user😀123", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isValidUID(tt.uid); got != tt.want {
				t.Errorf("isValidUID() = %v, want %v", got, tt.want)
			}
		})
	}
}
