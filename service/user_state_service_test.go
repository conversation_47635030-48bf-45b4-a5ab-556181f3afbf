package service

import (
	"context"
	"fmt"
	"strconv"
	"testing"
	"time"

	"git.7k7k.com/data/abScheduler/infra/config"
	"git.7k7k.com/data/abScheduler/infra/redises"
	"git.7k7k.com/data/abScheduler/repository/mysql"
	"git.7k7k.com/pkg/storage/codec"
	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupUserStateService(t *testing.T) (*UserStateService, *miniredis.Miniredis) {
	configService, mr := setupConfigService(t)
	rdb := redis.NewClient(&redis.Options{Addr: mr.Addr()})
	db := setupMongo(t)
	db.GetDB("abtest").Collection("user_state").DropCollection(context.Background())
	metricService := &MetricService{redises.Use(rdb), time.Unix(0, 0)}

	mysqlDB := setupTestDB()

	service, _ := NewUserStateService(
		&config.Config{
			MQ: config.MQ{
				Kafka: map[string]config.Kafka{
					"abtest": {
						Addrs: []string{"192.168.100.207:9092"},
					},
				},
			},
		},
		redises.Use(rdb),
		db,
		mysql.NewUserStateRepo(mysqlDB),
		configService,
		NewNotifyService(redises.Use(rdb)),
		metricService,
	)
	// service := &UserStateService{
	// 	cache:          rdb,
	// 	updateBuf:      make(chan []model.UserState, 100),
	// 	configService:  configService,
	// 	userStateMongo: db["abtest_unit_testing"].Collection("user_state"),
	// 	redisClts:      redises.Use(rdb),
	// 	notifyService:  NewNotifyService(redises.Use(rdb)),
	// }

	return service, mr
}

func TestUserStateService_Get(t *testing.T) {
	service, mr := setupUserStateService(t)

	mock := createMockData()
	tests := []struct {
		name      string
		prjID     int
		userID    string
		setupData map[int]*HitState
		wantErr   bool
	}{
		{
			name:   "正常获取数据",
			prjID:  int(mock.projects[0].ID),
			userID: "test_user_1",
			setupData: map[int]*HitState{
				int(mock.layers[0].ID): {GroupID: int(mock.groups[0].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
				int(mock.layers[1].ID): {GroupID: int(mock.groups[0].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
			},
		},
		{
			name:      "空数据",
			prjID:     int(mock.projects[0].ID),
			userID:    "test_user_2",
			setupData: map[int]*HitState{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			key := service.buildCacheKey(tt.prjID, tt.userID)

			// 设置测试数据
			for layerID, gs := range tt.setupData {
				value, err := codec.Default.MarshalToString(gs)
				require.NoError(t, err)
				mr.HSet(key, strconv.Itoa(layerID), value)
			}

			// 执行测试
			got, err := service.Gets(ctx, tt.prjID, tt.userID)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, len(tt.setupData), len(got))
			for layerID, wantGS := range tt.setupData {
				gotGS, exists := got[layerID]
				assert.True(t, exists)
				assert.Equal(t, wantGS, gotGS)
			}
		})
	}
}

func TestUserStateService_Update(t *testing.T) {
	service, mr := setupUserStateService(t)
	mock := createMockData()

	tests := []struct {
		name       string
		prjID      int
		userID     string
		setupData  map[int]*HitState // 初始数据
		updateData map[int]*HitState // 要更新的数据
		wantErr    bool
	}{
		{
			name:   "基本更新用例",
			prjID:  int(mock.projects[0].ID),
			userID: "test_user_1",
			setupData: map[int]*HitState{
				int(mock.layers[0].ID): {GroupID: int(mock.groups[0].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
			},
			updateData: map[int]*HitState{
				int(mock.layers[0].ID): {GroupID: int(mock.groups[1].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
			},
		},
		{
			name:   "更新多个层",
			prjID:  int(mock.projects[0].ID),
			userID: "test_user_2",
			setupData: map[int]*HitState{
				int(mock.layers[0].ID): {GroupID: int(mock.groups[0].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
				int(mock.layers[1].ID): {GroupID: int(mock.groups[1].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
			},
			updateData: map[int]*HitState{
				int(mock.layers[0].ID): {GroupID: int(mock.groups[2].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
				int(mock.layers[1].ID): {GroupID: int(mock.groups[1].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
			},
		},
		{
			name:   "新增层",
			prjID:  int(mock.projects[0].ID),
			userID: "test_user_3",
			setupData: map[int]*HitState{
				int(mock.layers[0].ID): {GroupID: int(mock.groups[0].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
			},
			updateData: map[int]*HitState{
				int(mock.layers[0].ID): {GroupID: int(mock.groups[0].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
				int(mock.layers[1].ID): {GroupID: int(mock.groups[1].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
			},
		},
		{
			name:   "删除层",
			prjID:  int(mock.projects[0].ID),
			userID: "test_user_4",
			setupData: map[int]*HitState{
				int(mock.layers[0].ID): {GroupID: int(mock.groups[0].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
				int(mock.layers[1].ID): {GroupID: int(mock.groups[1].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
			},
			updateData: map[int]*HitState{
				int(mock.layers[0].ID): {GroupID: int(mock.groups[0].ID), EnterAt: 1609459200, ExpiredAt: 1640995200},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			key := service.buildCacheKey(tt.prjID, tt.userID)

			// 设置初始数据
			if len(tt.setupData) > 0 {
				for layerID, gs := range tt.setupData {
					value, err := codec.Default.MarshalToString(gs)
					require.NoError(t, err)
					mr.HSet(key, strconv.Itoa(layerID), value)
				}
			}

			// 执行更新
			err := service.Update(ctx, tt.prjID, tt.userID, tt.updateData, tt.setupData)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)

			// 验证更新结果
			for layerID, wantGS := range tt.updateData {
				value := mr.HGet(key, strconv.Itoa(layerID))
				var gotGS *HitState
				err = codec.Default.UnmarshalFromString(value, &gotGS)
				require.NoError(t, err)
				assert.Equal(t, wantGS, gotGS)
			}

			// 验证被删除的层
			if len(tt.setupData) > len(tt.updateData) {
				for layerID := range tt.setupData {
					if _, exists := tt.updateData[layerID]; !exists {
						// 验证该层是否已被删除
						value := mr.HGet(key, strconv.Itoa(layerID))
						assert.Empty(t, value, "layer %d should be deleted", layerID)
					}
				}
			}
		})
	}
}

func TestUserStateService_CountGrpStateToRedis(t *testing.T) {
	service, _ := setupUserStateService(t)

	ctx := context.Background()
	grpId := int64(100)

	// 准备测试数据
	coll := service.mongoRepo.Coll()
	_, err := coll.InsertMany(ctx, []interface{}{
		map[string]interface{}{
			"uid":      "user1",
			"grp_id":   int32(grpId), // 确保使用int32类型
			"exp_id":   int32(1),
			"enter_at": time.Now(),
		},
		map[string]interface{}{
			"uid":      "user2",
			"grp_id":   int32(grpId),
			"exp_id":   int32(1),
			"enter_at": time.Now(),
		},
		map[string]interface{}{
			"uid":      "user3",
			"grp_id":   int32(grpId + 1), // 不同组
			"exp_id":   int32(2),
			"enter_at": time.Now(),
		},
	})
	require.NoError(t, err)

	// 测试计数功能
	_, err = service.CountGrpStateToRedis(ctx, grpId)
	require.NoError(t, err)

	// 验证Redis中的数据
	cli := service.redisClts.AdminRedis
	val, err := cli.Get(ctx, fmt.Sprintf("real_g_u:%d", grpId)).Int64()
	require.NoError(t, err)
	assert.Equal(t, int64(2), val) // 应该只统计到2个用户

	// 测试不存在的组
	_, err = service.CountGrpStateToRedis(ctx, grpId+999)
	require.NoError(t, err)
	val, err = cli.Get(ctx, fmt.Sprintf("real_g_u:%d", grpId+999)).Int64()
	require.NoError(t, err)
	assert.Equal(t, int64(0), val) // 不存在的组应该返回0
}
